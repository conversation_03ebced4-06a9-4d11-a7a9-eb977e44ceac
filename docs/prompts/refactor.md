# searchProducts Composable TypeScript Migration Plan

## 概要

## 現状分析

### 現在の実装構造

```javascript
// auction-side/src/composables/searchProducts.ts
export default function useSearchProducts() {
  // 主要な機能:
  // - search() - メイン検索（ストア更新）
  // - searchScope() - 検索（レスポンス処理）
  // - searchSuccessfulBidHistory() - 落札履歴検索
  // - searchAllSuccessfulBidHistory() - 全落札履歴
  // - searchWithModels() - カテゴリベース検索
  // - getConstants() - 検索定数取得
}
```

### 使用パターンの特定

1. **FilterBox Component** (`search-list/parts/FilterBox.vue`)

   - `search()`, `searchSuccessfulBidHistory()`, `searchAllSuccessfulBidHistory()` を使用
   - 主要な検索インターフェース

2. **BidConfirmModal Component** (`detail/BidConfirmModal.vue`)

   - 入札後のリフレッシュで `search()` を使用

3. **MyPage Components**

   - `bidding/` - 入札中アイテムのフィルタ検索
   - `favorite/` - お気に入りアイテムの検索

4. **Top Page Components**
   - `searchWithModels()` でカテゴリフィルタリング

### 現在の問題点

1. **コード重複**: `search()` と `searchScope()` でパラメータ構築ロジックが重複
2. **タイプセーフティ不足**: APIレスポンスやパラメータの型定義なし
3. **エラーハンドリング**: 汎用的なエラー処理、具体的なエラー型なし
4. **パフォーマンス**: パラメータの不要な再計算
5. **保守性**: 大きな関数で責任が混在

## TypeScript移行戦略

### フェーズ1: 型定義作成

#### API レスポンス型

```typescript
// 検索結果レスポンス
type SearchAuctionItemsResponse = {
  items: AuctionItem[]
  count: number
  exhibition_group: ExhibitionGroup[]
  isMoreLimit?: boolean
}

// オークションアイテム
type AuctionItem = {
  exhibition_item_no: string
  item_no: string
  category_id: number
  free_field: {
    productName: string
    image_url?: string
    start_price?: number
  }
  bid_status: {
    current_price: number
    bid_price: number
    bid_quantity: number
    tax_rate: number
    pitch_width?: number
  }
  attention_info: {
    bid_count: number
  }
  start_datetime: string
  end_datetime: string
  exhibition_no: string
}

// 展示グループ
type ExhibitionGroup = {
  exhibition_no: string
  exhibition_name: string
  start_datetime: string
  end_datetime: string
}
```

#### 検索パラメータ型

```typescript
// 基本検索パラメータ
type SearchParams = {
  category?: string | null
  searchKey?: string | null
  unSoldOut?: boolean
  favorite?: boolean | null
  bidding?: boolean
  auction_classification?: AuctionClassificationId | null
  exhibitionNos?: string[]
  initLimit?: number
  languageCode?: string
  limit?: number
  showedItemNos?: string[] | null
  startPrice?: number | null
  endPrice?: number | null
  sorter?: string | null
  timeOver?: string | null
  modelList?: string[]
  categoryList?: string[]
  exhibitionItemNos?: string[] | null
  brandList?: string[]
}

// 厳密なユニオン型
type ClassificationType = 'ascending' | 'sealed'
type AuctionClassificationId = 1 | 2

// 落札履歴検索パラメータ
type BidHistoryParams = {
  auctionClassification?: AuctionClassificationId[]
  startDatetime?: string
  endDatetime?: string
  initLimit?: number
  searchKey?: string | null
  categoryList?: string[]
}
```

#### フォーマット済みデータ型

```typescript
// フォーマット済み商品データ
type FormattedAuctionItem = AuctionItem & {
  link: string
  currentPrice: string
  currentPriceTaxIncluded: string
  noOfBids: number
  endDatePart: string
  endTimePart: string
  startDatePart: string
  startTimePart: string
  bidPrice: string
  bidQuantity: string
  bidInputError: {
    bidPrice: string | null
    bidQuantity: string | null
  }
}

// 商品リスト構造
type ProductList = {
  all: FormattedAuctionItem[]
  exhibitionList: ExhibitionGroup[]
}
```

### フェーズ2: 最適化されたComposable構造

#### 共通パラメータ構築関数

```typescript
/**
 * 検索パラメータを構築する共通関数
 * @param inputParams - 入力パラメータ
 * @param store - 検索結果ストア
 * @param locale - 現在のロケール
 * @returns 構築された検索パラメータ
 */
const buildSearchParams = (
  inputParams: Partial<SearchParams>,
  store: ReturnType<typeof useSearchResultStore>,
  locale: string
): SearchParams => {
  const {
    category = null,
    exhibitionNos = [],
    searchKey = store.searchKey,
    unSoldOut = store.unSoldOut,
    favorite = store.favorite,
    bidding = false,
    initLimit = store.showCount * store.viewMore,
    modelList = store.modelList,
    categoryList = store.categoryList,
    exhibitionItemNos = null,
    startPrice = store.startPrice ? Number(store.startPrice) : null,
    endPrice = store.endPrice ? Number(store.endPrice) : null,
    sorter = store.sorter,
    brandList = store.brandList,
    auction_classification = null,
  } = inputParams

  // categoryList の正規化
  let tempCategoryList = Array.isArray(categoryList) ? categoryList : []
  if (tempCategoryList.length === 1 && tempCategoryList[0] === null) {
    tempCategoryList = []
  } else if (favorite || bidding) {
    tempCategoryList = []
  }

  // brandList の正規化
  let tempBrandList = Array.isArray(brandList) ? brandList : []
  if (tempBrandList.length === 1 && tempBrandList[0] === null) {
    tempBrandList = []
  }

  return {
    category,
    searchKey,
    unSoldOut,
    favorite,
    bidding,
    auction_classification,
    exhibitionNos,
    initLimit,
    languageCode: locale,
    limit: initLimit,
    showedItemNos: null,
    startPrice,
    endPrice,
    sorter,
    timeOver: null,
    modelList,
    categoryList: tempCategoryList,
    exhibitionItemNos,
    brandList: tempBrandList,
  }
}
```

#### エラーハンドリング型

```typescript
// API エラー型
type ApiError = {
  message: string
  code?: string
  status?: number
}

// エラーハンドリング関数
const handleSearchError = (
  error: unknown,
  parseHtmlResponseError: (error: any) => ApiError,
  dialog: ReturnType<typeof useMessageDialogStore>,
  t: (key: string) => string
): void => {
  const parsedError = parseHtmlResponseError(error)
  console.error('Search error:', parsedError)
  dialog.setShowMessage(parsedError.message ?? t('common.error'), {isErr: true})
}
```

#### 最適化されたメイン関数

```typescript
/**
 * メイン検索関数 - ストアを更新
 * @param inputParams - 検索パラメータ
 */
const search = async (
  inputParams: Partial<SearchParams> = {}
): Promise<void> => {
  loading.value = true

  try {
    const params = buildSearchParams(inputParams, store, locale.value)
    const response = await apiExecute<SearchAuctionItemsResponse>(
      'public/search-auction-items',
      params
    )
    store.setProductList(response)
  } catch (error) {
    handleSearchError(error, parseHtmlResponseError, dialog, t)
  } finally {
    loading.value = false
  }
}

/**
 * スコープ検索関数 - レスポンス処理のみ
 * @param inputParams - 検索パラメータ
 */
const searchScope = async (
  inputParams: Partial<SearchParams> = {}
): Promise<void> => {
  loading.value = true

  try {
    const params = buildSearchParams(inputParams, store, locale.value)
    const response = await apiExecute<SearchAuctionItemsResponse>(
      'public/search-auction-items',
      params
    )
    setProductListFromResponse(response)
  } catch (error) {
    handleSearchError(error, parseHtmlResponseError, dialog, t)
  } finally {
    loading.value = false
  }
}
```

### フェーズ3: コンポーネント更新戦略

#### 更新対象コンポーネント

1. **FilterBox.vue** (`search-list/parts/FilterBox.vue`)

   ```typescript
   // Before (JavaScript)
   const {search, searchSuccessfulBidHistory} = useSearchProducts()

   // After (TypeScript)
   const {search, searchSuccessfulBidHistory} = useSearchProducts()
   // 型安全性が自動的に適用される
   ```

2. **BidConfirmModal.vue** (`detail/BidConfirmModal.vue`)

   ```typescript
   // TypeScript化での変更点
   <script setup lang="ts">
   import useSearchProducts from '@/composables/useSearchProducts'

   const { search: searchList } = useSearchProducts()
   // 型推論により自動補完とエラー検出が有効
   </script>
   ```

3. **MyPage Components**
   - `bidding/` コンポーネント群
   - `favorite/` コンポーネント群
   - 既存の `useClassificationSwitch.ts` との連携

#### 段階的移行アプローチ

```typescript
// 移行期間中の互換性維持
// .vue ファイルは拡張子を維持し、<script setup lang="ts"> を使用
<script setup lang="ts">
import useSearchProducts from '@/composables/useSearchProducts'
import type { SearchParams } from '@/composables/useSearchProducts'

// 型安全な使用例
const { search, loading } = useSearchProducts()

const handleSearch = async () => {
  const params: Partial<SearchParams> = {
    favorite: true,
    auction_classification: 1
  }
  await search(params)
}
</script>
```

## 実装フェーズ詳細

### フェーズ1: 分析・設計 (1-2日)

#### タスク1.1: 現在の実装詳細分析

- [ ] 全関数の入力・出力パラメータ詳細化
- [ ] データフロー図作成
- [ ] 依存関係マッピング

#### タスク1.2: 使用パターンマッピング

- [ ] 各コンポーネントでの使用方法調査
- [ ] パラメータ使用頻度分析
- [ ] エラーケース特定

#### タスク1.3: 最適化機会特定

- [ ] 重複コード箇所リスト化
- [ ] パフォーマンス改善ポイント特定
- [ ] メモリ使用量分析

### フェーズ2: 型定義作成 (2-3日)

#### タスク2.1: API レスポンス型定義

```typescript
// 実際のAPIレスポンスに基づく詳細型定義
type SearchAuctionItemsResponse = {
  items: AuctionItem[]
  count: number
  exhibition_group: ExhibitionGroup[]
  isMoreLimit?: boolean
  // 追加フィールドの調査と定義
}
```

#### タスク2.2: 検索パラメータ型定義

```typescript
// 厳密なバリデーション型
type SearchParams = {
  // 各フィールドの詳細制約定義
  category?: string | null
  searchKey?: string | null
  // ... 他のフィールド
}
```

#### タスク2.3: ユーティリティ型作成

```typescript
// データ変換用の型定義
type DataTransformOptions = {
  formatPrice: boolean
  includeImages: boolean
  calculateTax: boolean
}
```

### フェーズ3: Composable移行 (3-4日)

#### タスク3.1: TypeScript変換

- [ ] 型注釈追加
- [ ] エラーハンドリング改善

#### タスク3.2: 最適化実装

- [ ] 共通パラメータ構築関数作成
- [ ] 重複ロジック統合
- [ ] パフォーマンス改善

#### タスク3.3: JSDoc拡充

````typescript
/**
 * オークションアイテムを検索する
 *
 * @param inputParams - 検索パラメータ
 * @param inputParams.category - カテゴリフィルタ
 * @param inputParams.searchKey - 検索キーワード
 * @param inputParams.favorite - お気に入りのみ表示
 * @param inputParams.bidding - 入札中のみ表示
 * @param inputParams.auction_classification - オークション種別 (1=競り上がり, 2=封印)
 *
 * @returns Promise<void> - 検索完了時に解決
 *
 * @example
 * ```typescript
 * // 基本検索
 * await search({ searchKey: 'キーワード' })
 *
 * // お気に入り検索
 * await search({ favorite: true, auction_classification: 1 })
 *
 * // 入札中アイテム検索
 * await search({ bidding: true, unSoldOut: true })
 * ```
 */
````

### フェーズ4: コンポーネント更新 (2-3日)

#### タスク4.1: FilterBox コンポーネント更新

```typescript
// search-list/parts/FilterBox.vue
<script setup lang="ts">
import useSearchProducts from '@/composables/useSearchProducts'
import type { SearchParams } from '@/composables/useSearchProducts'

const {
  search,
  searchSuccessfulBidHistory,
  searchAllSuccessfulBidHistory,
  loading
} = useSearchProducts()

// 型安全な検索実行
const handleSearch = async (): Promise<void> => {
  const params: Partial<SearchParams> = {
    favorite: route.path === PATH_NAME.MYPAGE_FAVORITE,
    bidding: route.path === PATH_NAME.MYPAGE_BIDDING,
    auction_classification: classification.value
  }

  await search(params)
}
</script>
```

#### タスク4.2: MyPage コンポーネント群更新

- `bidding/` コンポーネント
- `favorite/` コンポーネント
- 既存の `useClassificationSwitch.ts` との型安全な連携

#### タスク4.3: BidConfirmModal 更新

```typescript
// detail/BidConfirmModal.vue
<script setup lang="ts">
import useSearchProducts from '@/composables/useSearchProducts'

const { search: searchList } = useSearchProducts()

// 入札後のリフレッシュ処理
const refreshAfterBid = async (): Promise<void> => {
  if (route.path === PATH_NAME.MYPAGE_FAVORITE) {
    await searchList({
      favorite: true,
      auction_classification: isAscendingAuction ? 1 : 2
    })
  } else if (route.path === PATH_NAME.MYPAGE_BIDDING) {
    await searchList({
      bidding: true,
      unSoldOut: true,
      auction_classification: isAscendingAuction ? 1 : 2
    })
  }
}
</script>
```

### フェーズ5: テスト作成 (2-3日)

#### タスク5.1: ユニットテスト作成

```typescript
// useSearchProducts.test.ts
import {describe, it, expect, vi, beforeEach} from 'vitest'
import {useSearchProducts} from '@/composables/useSearchProducts'

describe('useSearchProducts', () => {
  beforeEach(() => {
    vi.resetAllMocks()
  })

  describe('search function', () => {
    it('基本検索パラメータを正しく構築する', async () => {
      const {search} = useSearchProducts()
      const mockApiExecute = vi.fn().mockResolvedValue({items: [], count: 0})

      await search({searchKey: 'test'})

      expect(mockApiExecute).toHaveBeenCalledWith(
        'public/search-auction-items',
        expect.objectContaining({
          searchKey: 'test',
          languageCode: 'ja',
        })
      )
    })

    it('お気に入り検索を正しく処理する', async () => {
      const {search} = useSearchProducts()

      await search({favorite: true, auction_classification: 1})

      // アサーション
    })
  })

  describe('error handling', () => {
    it('API エラーを適切に処理する', async () => {
      // エラーハンドリングテスト
    })
  })
})
```

#### タスク5.2: 統合テスト作成

```typescript
// integration/searchProducts.integration.test.ts
describe('SearchProducts Integration', () => {
  it('実際のAPIレスポンスを正しく処理する', async () => {
    // モックAPIレスポンスでの統合テスト
  })

  it('ストアとの連携が正しく動作する', async () => {
    // ストア連携テスト
  })
})
```

#### タスク5.3: コンポーネント統合テスト

```typescript
// components/FilterBox.test.ts
import {mount} from '@vue/test-utils'
import FilterBox from '@/components/search-list/parts/FilterBox.vue'

describe('FilterBox Component', () => {
  it('TypeScript composable と正しく連携する', async () => {
    // コンポーネントテスト
  })
})
```

### フェーズ6: パフォーマンス最適化 (1-2日)

#### タスク6.1: パフォーマンス測定

- [ ] 現在の実装のベンチマーク取得
- [ ] メモリ使用量測定
- [ ] API呼び出し回数分析

#### タスク6.2: 最適化実装

```typescript
// パラメータキャッシュ機能
const parameterCache = new Map<string, SearchParams>()

const buildSearchParamsWithCache = (
  inputParams: Partial<SearchParams>,
  store: ReturnType<typeof useSearchResultStore>,
  locale: string
): SearchParams => {
  const cacheKey = JSON.stringify({inputParams, storeState: store.$state})

  if (parameterCache.has(cacheKey)) {
    return parameterCache.get(cacheKey)!
  }

  const params = buildSearchParams(inputParams, store, locale)
  parameterCache.set(cacheKey, params)

  return params
}
```

#### タスク6.3: リクエスト重複排除

```typescript
// 同時リクエスト制御
let currentSearchPromise: Promise<void> | null = null

const search = async (
  inputParams: Partial<SearchParams> = {}
): Promise<void> => {
  if (currentSearchPromise) {
    await currentSearchPromise
  }

  currentSearchPromise = performSearch(inputParams)

  try {
    await currentSearchPromise
  } finally {
    currentSearchPromise = null
  }
}
```

### フェーズ7: ドキュメント作成 (1日)

#### タスク7.1: 移行ガイド作成

````markdown
# TypeScript Migration Guide

## 移行前後の比較

### Before (JavaScript)

```javascript
const {search} = useSearchProducts()
await search({searchKey: 'keyword'})
```
````

### After (TypeScript)

```typescript
const {search} = useSearchProducts()
await search({searchKey: 'keyword'}) // 型安全性が自動適用
```

## 型定義の活用

### 検索パラメータの型安全性

```typescript
import type {SearchParams} from '@/composables/useSearchProducts'

const params: Partial<SearchParams> = {
  favorite: true,
  auction_classification: 1, // 1 | 2 のみ許可
}
```

````

#### タスク7.2: 使用例とベストプラクティス
```typescript
// ベストプラクティス例

// ✅ 推奨: 型安全なパラメータ使用
const searchFavorites = async (classification: 'ascending' | 'sealed') => {
  await search({
    favorite: true,
    auction_classification: classification === 'ascending' ? 1 : 2
  })
}

// ❌ 非推奨: 型チェックを回避
const searchUnsafe = async () => {
  await search({
    auction_classification: 3 as any // コンパイルエラーを回避
  })
}

// ✅ 推奨: エラーハンドリング
const searchWithErrorHandling = async () => {
  try {
    await search({ searchKey: 'keyword' })
  } catch (error) {
    // 型安全なエラーハンドリング
    console.error('Search failed:', error)
  }
}
````

## 期待される効果

### 1. 開発効率向上

- **自動補完**: IDEでの関数・パラメータ補完
- **エラー検出**: コンパイル時のタイプエラー検出
- **リファクタリング**: 安全な名前変更・構造変更

### 2. コード品質向上

- **型安全性**: ランタイムエラーの事前防止
- **可読性**: 明確な型定義による理解しやすさ
- **保守性**: 変更影響範囲の明確化

### 3. パフォーマンス改善

- **重複排除**: 同一パラメータでの重複API呼び出し防止
- **キャッシュ**: パラメータ構築結果のキャッシュ
- **最適化**: 不要な再計算の削減

### 4. 開発者体験向上

- **VSCode統合**: Find References、Go to Definition の改善
- **JSDoc**: 詳細な関数説明とサンプルコード
- **型推論**: 明示的な型注釈の削減

## リスク管理

### 移行リスク

1. **互換性**: 既存コンポーネントとの互換性確保
2. **学習コスト**: TypeScript習得コスト
3. **ビルド時間**: 型チェックによるビルド時間増加

### 対策

1. **段階的移行**: 一度に全てを変更せず段階的に実施
2. **テスト強化**: 移行前後での動作確認テスト
3. **ドキュメント**: 詳細な移行手順とトラブルシューティング

## 成功指標

### 定量的指標

- [ ] TypeScript化率: 100%
- [ ] テストカバレッジ: 90%以上
- [ ] ビルドエラー: 0件
- [ ] 型エラー: 0件

### 定性的指標

- [ ] 開発者の満足度向上
- [ ] バグ発生率の減少
- [ ] 新機能開発速度の向上
- [ ] コードレビュー効率の改善

## 今後の展開

### 他のComposableへの適用

1. `useApi.ts` の型定義強化
2. `useAuth.ts` の型安全性向上
3. ストア系の型定義統一

### 継続的改善

1. 型定義の定期的な見直し
2. パフォーマンス監視と最適化
3. 新しいTypeScript機能の活用検討

---

## 実装チェックリスト

### 準備フェーズ

- [ ] 現在の実装分析完了
- [ ] 使用パターン調査完了
- [ ] 最適化ポイント特定完了

### 実装フェーズ

- [ ] 型定義作成完了
- [ ] Composable TypeScript化完了
- [ ] コンポーネント更新完了
- [ ] テスト作成完了

### 検証フェーズ

- [ ] ユニットテスト全通過
- [ ] 統合テスト全通過
- [ ] パフォーマンステスト完了
- [ ] 型チェック全通過

### リリースフェーズ

- [ ] ドキュメント作成完了
- [ ] 移行ガイド作成完了
- [ ] チームレビュー完了
- [ ] 本番デプロイ完了

この計画に従って段階的に実装を進めることで、安全かつ効率的にTypeScript移行を完了できます。
