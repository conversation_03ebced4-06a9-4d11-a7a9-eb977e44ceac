<script setup>
  defineOptions({
    name: 'TermsComponent', // avoid error "component name should always be multi-word"
  })

  import {ref, watch} from 'vue'
  import {useLocale} from 'vuetify'
  import TermsEN from './Locale/TermsEN.vue'
  import TermsJA from './Locale/TermsJA.vue'

  const {current} = useLocale()
  const currentLocale = ref(current.value)

  watch(
    () => current.value,
    () => {
      currentLocale.value = current.value
    }
  )
</script>

<template>
  <TermsJA v-if="currentLocale === 'ja'" />
  <TermsEN v-else />
</template>
