<script setup>
  import {ref, watch} from 'vue'
  import {useLocale} from 'vuetify'
  import PrivacyPageEN from './Locale/PrivacyPageEN.vue'
  import PrivacyPageJA from './Locale/PrivacyPageJA.vue'

  const {current} = useLocale()
  const currentLocale = ref(current.value)

  watch(
    () => current.value,
    () => {
      currentLocale.value = current.value
    }
  )
</script>

<template>
  <PrivacyPageJA v-if="currentLocale === 'ja'" />
  <PrivacyPageEN v-else />
</template>
