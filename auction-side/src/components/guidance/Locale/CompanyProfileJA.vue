<script>
  import {defineAsyncComponent} from 'vue'

  export default {
    name: 'CompanyProfileJA',
    components: {
      BreadCrumb: defineAsyncComponent(
        () => import('../../../components/common/BreadCrumb.vue')
      ),
    },
  }
</script>

<template>
  <div>
    <BreadCrumb />
    <h1 class="mb0">会社概要</h1>
    <section id="static">
      <div class="container">
        <table class="tbl-otherItem">
          <tbody>
            <tr>
              <th>会社名</th>
              <td>株式会社ワールドモバイル(ゲオグループ)</td>
            </tr>
            <tr>
              <th>会社設立</th>
              <td>2017年(平成29年)2月1日</td>
            </tr>
            <tr>
              <th>役員</th>
              <td>
                <ol>
                  <li>代表取締役社長 森田広史</li>
                  <li>取締役 宮寧</li>
                  <li>取締役 富田浩計</li>
                </ol>
              </td>
            </tr>
            <tr>
              <th>本社</th>
              <td>〒460-0014 愛知県名古屋市中区富士見町8-8 OMCビル</td>
            </tr>
            <tr>
              <th>東京本部</th>
              <td>〒170-0005 東京都豊島区南大塚3丁目53番11号 今井三菱ビル</td>
            </tr>
            <tr>
              <th>東京プロセスセンター</th>
              <td>
                〒135-0062 東京都江東区東雲2-9-51 日本通運東雲流通センター7F
              </td>
            </tr>
            <tr>
              <th>電話番号</th>
              <td>03-6426-0826</td>
            </tr>
            <tr>
              <th>資本金</th>
              <td>10百万円</td>
            </tr>
            <tr>
              <th>事業内容</th>
              <td>通信機器の販売・買取</td>
            </tr>
          </tbody>
        </table>
      </div>
    </section>
  </div>
</template>
