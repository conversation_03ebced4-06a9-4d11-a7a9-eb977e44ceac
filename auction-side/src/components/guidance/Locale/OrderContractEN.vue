<script>
  import {defineAsyncComponent} from 'vue'

  export default {
    name: 'OrderContractEN',
    components: {
      BreadCrumb: defineAsyncComponent(
        () => import('../../../components/common/BreadCrumb.vue')
      ),
    },
  }
</script>

<template>
  <div>
    <BreadCrumb />
    <h1 class="mb0">Notation Based on Specified Commercial Transactions Law</h1>
    <section id="static">
      <div class="container">
        <table class="tbl-otherItem">
          <tbody>
            <tr>
              <th>Company Name</th>
              <td>World Mobile Corporation</td>
            </tr>
            <tr>
              <th>Representative</th>
              <td>Hirofumi Morita</td>
            </tr>
            <tr>
              <th>Head Office Location</th>
              <td>
                OMC Building, 8-8 Fujimi-cho, Naka-ku, Nagoya-shi, Aichi
                Prefecture
              </td>
            </tr>
            <tr>
              <th>Tokyo Office</th>
              <td>
                Imai Mitsubishi Building, 3-53-11 Minami-Otsuka, Toshima-ku,
                Tokyo
              </td>
            </tr>
            <tr>
              <th>Phone Number</th>
              <td>03-6426-0826</td>
            </tr>
            <tr>
              <th>Store Operations Manager</th>
              <td>Shoji Manabe</td>
            </tr>
          </tbody>
        </table>
        <h3 class="ttl-kobutsu">
          Display Based on Secondhand Articles Dealer Law
        </h3>
        <table class="tbl-otherItem">
          <tbody>
            <tr>
              <th>Secondhand Articles Dealer License Number</th>
              <td>
                <ol>
                  <li>Aichi Prefectural Public Safety Commission</li>
                  <li>No. 541161702600</li>
                  <li>Obtained on April 26, 2017</li>
                </ol>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </section>
  </div>
</template>
