<script setup>
  import {onMounted, ref, watch} from 'vue'
  import {useLocale} from 'vuetify'
  import GuidePageEN from './Locale/GuidePageEN.vue'
  import GuidePageJA from './Locale/GuidePageJA.vue'

  const {current} = useLocale()
  const currentLocale = ref(current.value)

  watch(
    () => current.value,
    () => {
      currentLocale.value = current.value
    }
  )

  onMounted(() => {
    console.log('mounted GuidePage')
  })
</script>

<template>
  <GuidePageJA v-if="currentLocale === 'ja'" />
  <GuidePageEN v-else />
</template>
