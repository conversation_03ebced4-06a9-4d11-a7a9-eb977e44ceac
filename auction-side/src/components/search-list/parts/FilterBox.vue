<script setup>
  import {computed, ref, toRaw, watch} from 'vue'
  import {onBeforeRouteLeave, useRoute} from 'vue-router'
  import {useLocale} from 'vuetify'
  import useSearchProducts from '../../../composables/searchProducts'
  import useSearchResultState from '../../../composables/useSearchResultState'
  import {PATH_NAME} from '../../../defined/const'

  const props = defineProps(['classification'])

  const {t} = useLocale()
  const route = useRoute()
  const {
    search,
    searchWithModels,
    searchSuccessfulBidHistory,
    searchAllSuccessfulBidHistory,
  } = useSearchProducts()
  const state = useSearchResultState()
  const localSearchKey = ref('')

  const count = computed(() => state.totalCount)
  const categories = computed(() => {
    const constants = state.constants
    const constantsArray = Array.isArray(constants.value) ? constants.value : []
    return constantsArray
      .filter(x => x && x.key_string === 'PRODUCT_CATEGORY')
      .map(x => {
        return {
          id: x.value1,
          name: x.value2,
          checked: state.categoryList.indexOf(x.value1) !== -1,
        }
      })
  })
  const classification = computed(() => toRaw(props.classification))

  // "checked": trueがあるカテゴリーのみが含まれる。
  const filterCategories = computed(() => {
    return categories.value.filter(x => x.checked).map(x => x.name)
  })

  // state.searchKeyTop に基づいて「conditions」をリアクティブに更新するwatch
  watch(
    () => state.searchKeyTop,
    newVal => {
      state.searchKey.value = newVal
      localSearchKey.value = newVal
    },
    {immediate: true}
  )

  const handleSearch = async () => {
    state.searchKey.value = localSearchKey.value
    state.viewMore.value = 1

    // Update checked categories
    const catList = categories.value.filter(x => x.checked).map(y => y.id)
    state.categoryList.length = 0
    catList.map(x => state.categoryList.push(x))

    if (route.path === PATH_NAME.MYPAGE_BID_HISTORY) {
      await searchSuccessfulBidHistory()
    } else if (route.path === PATH_NAME.BID_HISTORY_ALL) {
      await searchAllSuccessfulBidHistory()
    } else if (route.path === PATH_NAME.MYPAGE_FAVORITE) {
      search({
        favorite: true,
        auction_classification: classification.value,
      })
    } else if (route.path === PATH_NAME.MYPAGE_BIDDING) {
      search({
        bidding: true,
        unSoldOut: true,
        auction_classification: classification.value,
      })
    } else {
      // using in TOP
      await searchWithModels(null, {
        checkedCategories: categories.value
          .filter(x => x.checked)
          .map(y => y.id),
      })
    }
  }

  const handleReset = async () => {
    localSearchKey.value = null
    // Import reset composables when needed
    const useResetParams = (await import('../../../composables/useResetParams'))
      .default
    const resetParams = useResetParams()
    resetParams()
    await handleSearch()
  }

  onBeforeRouteLeave(async () => {
    const useResetParams = (await import('../../../composables/useResetParams'))
      .default
    const resetParams = useResetParams()
    resetParams()
  })

  const clearCategory = async cat => {
    const catId = categories.value.find(x => x.name === cat)?.id
    if (catId) {
      const filteredList = state.categoryList.filter(x => x !== catId)
      state.categoryList.length = 0
      filteredList.forEach(x => state.categoryList.push(x))
    }
    await handleSearch()
  }

  const clearSearchText = async () => {
    localSearchKey.value = null
    state.searchKey.value = null
    await handleSearch()
  }
</script>
<template>
  <section id="search-condition">
    <h1 v-if="route.path === PATH_NAME.TOP" class="ttl-home">
      <span class="">WorldMobile MobileAuction</span>
    </h1>
    <div class="container">
      <div class="panel-search-ope">
        <h2>{{ t('filterBox.title') }}</h2>
        <div class="cont">
          <div class="condition">
            <div class="keyword">
              <div class="keyword__label">{{ t('filterBox.keyword') }}</div>
              <input
                type="text"
                data-id="shop-search-keyword"
                class="search-keyword keyword__contents"
                :placeholder="t('filterBox.inputPlaceholder')"
                v-model="localSearchKey"
              />
            </div>
            <div class="model">
              <div class="model__label">{{ t('filterBox.category') }}</div>
              <div class="model__contents">
                <div v-for="cat in categories" :key="cat.id" class="label-item">
                  <input
                    :id="cat.id"
                    class="checkbox-model"
                    type="checkbox"
                    v-model="cat.checked"
                  />
                  <label :for="cat.id">
                    {{ cat.name }}
                  </label>
                </div>
              </div>
            </div>
          </div>
          <div class="btn-wrap">
            <button @click="handleSearch" class="btnBsc-search btn">
              {{ t('filterBox.searchButton') }}
            </button>
          </div>
        </div>
      </div>
      <div class="panel-results">
        <p class="num">
          <span>{{ count ?? 0 }}</span
          >{{ t('filterBox.auctionCount') }}
        </p>
        <div class="condition">
          <div class="condition__label">
            {{ t('filterBox.searchCriteria') }}
          </div>
          <div class="condition__contents">
            <div class="filter">
              <p v-if="localSearchKey">
                <span class="cat-name">{{ localSearchKey }}</span>
                <span class="close" @click="clearSearchText">×</span>
              </p>
              <p v-for="cat in filterCategories" :key="cat">
                <span class="cat-name">{{ cat }}</span>
                <span class="close" @click="clearCategory(cat)">×</span>
              </p>
            </div>
            <div class="clear-conditions">
              <button @click="handleReset">
                {{ t('filterBox.clearConditions') }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>
<style scoped lang="scss">
  #search-condition .container .panel-results .condition [class$='__label'] {
    width: 150px;
  }
</style>
