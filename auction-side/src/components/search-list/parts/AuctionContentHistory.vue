<script setup>
  import {computed, defineProps} from 'vue'
  import {useRoute} from 'vue-router'
  import {useLocale} from 'vuetify'
  import useSearchProducts from '../../../composables/searchProducts'
  import {PATH_NAME} from '../../../defined/const'
  import {useSearchResultStore} from '../../../stores/search-results'

  const props = defineProps(['productList'])

  const {t} = useLocale()
  const route = useRoute()
  const {totalCount} = useSearchResultStore()
  const {
    searchSuccessfulBidHistory,
    searchAllSuccessfulBidHistory,
    countUpViewMore,
  } = useSearchProducts()

  const isShowLoadMore = computed(() => {
    if (route.path === PATH_NAME.BID_HISTORY_ALL) {
      return (
        props.exhibitionInfo?.count &&
        props.productList &&
        props.productList.length < props.exhibitionInfo.count
      )
    }
    return props.productList && props.productList.length < totalCount
  })

  const localProductList = computed(() => props.productList)
  const count = computed(() => {
    if (route.path === PATH_NAME.BID_HISTORY_ALL) {
      return localProductList.value.length
    }
    return totalCount
  })

  const loadMore = () => {
    countUpViewMore()
    if (route.path === PATH_NAME.BID_HISTORY_ALL) {
      searchAllSuccessfulBidHistory()
    } else {
      searchSuccessfulBidHistory()
    }
  }
</script>
<template>
  <div class="auction-contents">
    <div class="status">
      <div class="num-results">
        <span>{{ count }}</span
        >{{ t('filterBox.auctionCount') }}
      </div>
    </div>

    <div class="list-item-table">
      <table
        :class="{
          'mypage-list': route.path !== PATH_NAME.TOP,
          favorite: route.path === PATH_NAME.MYPAGE_FAVORITE,
        }"
      >
        <thead class="">
          <slot name="header"></slot>
        </thead>
        <tbody>
          <template v-for="item in localProductList" :key="item.itemNo">
            <slot name="item" :item="item"></slot>
          </template>
        </tbody>
      </table>
      <div v-if="isShowLoadMore" class="wrap-btn list-more">
        <button class="btn" @click="loadMore">
          <span class="txt"
            >{{ t('common.more') }}<span class="arrow"></span
          ></span>
        </button>
      </div>
    </div>
  </div>
</template>
