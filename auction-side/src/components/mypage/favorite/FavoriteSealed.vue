<script setup lang="ts">
  import {formatDateString} from '@/composables/common'
  import useFavorite from '@/composables/favorite'
  import useFavoriteItems from '@/composables/useFavoriteItems'
  import useSearchResultState from '@/composables/useSearchResultState'
  import {computed, onMounted, watch} from 'vue'

  const searchState = useSearchResultState()
  const {loading, fetchFavoriteSealed} = useFavoriteItems()
  const {toggleFavorite} = useFavorite()

  // Filter items for sealed auctions only
  const sealedItems = computed(() => {
    return searchState.productList.all.filter(
      item => Number(item.auction_classification) === 2
    )
  })

  watch(
    () => searchState.productList,
    val => {
      console.log(
        '%c 🚩: val ',
        'font-size:16px;background-color:#32203c;color:white;',
        val
      )
      // Recalculate sealed items when product list changes
    },
    {immediate: true}
  )

  const totalCount = computed(() => searchState.totalCount.value)

  // Calculate remaining time for auction end
  const getRemainingTime = (endDatetime: string) => {
    const now = new Date()
    const end = new Date(endDatetime)
    const diff = end.getTime() - now.getTime()

    if (diff <= 0) return '終了'

    const hours = Math.floor(diff / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

    if (hours > 0) {
      return `残り ${hours}時間`
    } else {
      return `残り ${minutes}分`
    }
  }

  // Format end datetime for display
  const formatEndDateTime = (endDatetime: string) => {
    const {datePart, timePart} = formatDateString(endDatetime)
    return `（${datePart} ${timePart}終了）`
  }

  // Handle favorite toggle
  const handleFavoriteToggle = async (
    exhibitionItemNo: string,
    currentFavorited: boolean
  ) => {
    await toggleFavorite(exhibitionItemNo, currentFavorited)
    // Refresh the list after toggling favorite
    await fetchFavoriteSealed()
  }

  // Fetch data on component mount
  onMounted(async () => {
    await fetchFavoriteSealed()
  })
</script>

<template>
  <div class="display-option">
    <div class="refine">
      <div class="count">
        <p>
          <span>{{ totalCount }}</span
          >件のオークション
        </p>
      </div>
    </div>
  </div>

  <div v-if="loading" class="loading">読み込み中...</div>

  <div v-else class="item-list row-bid">
    <ul>
      <li
        v-for="item in sealedItems"
        :key="item.exhibition_item_no"
        :class="['box', {soldout: item.status === 'soldout'}]"
      >
        <figure>
          <img
            :src="
              item.free_field.image_url || '@/assets/img/item/top_item01.png'
            "
            alt=""
          />
          <div class="tab-f"><span class="title-a">New</span></div>
        </figure>

        <div class="item-p-desc">
          <p class="item-name">
            <a :href="item.link">
              {{ item.free_field.productName }}
              <span v-if="item.free_field.shipping_free" class="tab-item"
                >送料無料</span
              >
            </a>
          </p>

          <div class="desc-p-top">
            <div class="price-box">
              <p class="price">
                <span class="price-c">現在 : </span>
                <span class="price-v">{{ item.currentPrice }}</span>
                <span class="price-u">円</span>
                <span class="tax-u">（税込）</span>
              </p>
              <p class="price">
                <span class="price-c">即決価格 : </span>
                <span class="price-v bl">{{
                  item.free_field.instant_price?.toLocaleString()
                }}</span>
                <span class="price-u bl">円</span>
                <span class="tax-u">（税込）</span>
              </p>
              <p class="price">
                <span class="price-c">最低入札価格 : </span>
                <span class="price-v bl sm">{{
                  item.free_field.minimum_bid_price?.toLocaleString()
                }}</span>
                <span class="price-u bl sm">円</span>
                <span class="tax-u">（税込）</span>
              </p>
            </div>
            <ul class="tab-wrap-status">
              <li v-if="item.bid_status.is_top_member" class="top">
                あなたがTOP
              </li>
              <li v-if="item.bid_status.minimum_bid_exceeded" class="min-bid">
                最低落札超え
              </li>
            </ul>
          </div>

          <ul class="tab-wrap">
            <li class="tab-main">{{ item.free_field.condition }}</li>
            <li class="tab-sub">ICON</li>
            <li class="tab-wari">ICON</li>
          </ul>

          <ul class="pre-bid">
            <li class="view">
              <p>{{ item.attention_info.view_count }}</p>
            </li>
            <li class="favo">
              <p>{{ item.attention_info.favorited_count }}</p>
            </li>
            <li class="bid-v">
              <p>{{ item.attention_info.bid_count }}</p>
            </li>
            <li class="end-v">
              <p>
                <span class="date red">{{
                  getRemainingTime(item.end_datetime)
                }}</span>
                <span class="end">{{
                  formatEndDateTime(item.end_datetime)
                }}</span>
              </p>
            </li>
          </ul>
          <button
            :class="[
              'btn',
              'favorite',
              'row-bid',
              {active: item.attention_info.is_favorited},
            ]"
            @click="
              handleFavoriteToggle(
                item.exhibition_item_no,
                item.attention_info.is_favorited
              )
            "
          ></button>
        </div>
        <div class="place-bid">
          <div class="price">
            <span class="ttl">入札価格</span>
            <input
              type="text"
              data-id="price-bid"
              value=""
              class="price-bid"
              placeholder="1,000"
            />円
          </div>
          <ul class="bidding-unit">
            <li>
              <button class="bid-unit">
                <span class="icn_add"></span>¥10,000
              </button>
            </li>
            <li>
              <button class="bid-unit">
                <span class="icn_add"></span>¥50,000
              </button>
            </li>
            <li>
              <button class="bid-unit">
                <span class="icn_add"></span>¥100,000
              </button>
            </li>
          </ul>
          <div class="button-bid">
            <button class="btn">
              <img class="pct" src="@/assets/img/common/icn_bid_w.svg" />
              <span class="bid">入札する</span>
            </button>
            <p class="update"><span>更新</span></p>
          </div>
        </div>
      </li>
    </ul>
  </div>

  <div class="wrap-btn pagination">
    <p>{{ totalCount }}件中 1〜{{ sealedItems.length }}件を表示</p>
    <nav class="pagination">
      <ul>
        <li class="prev"><a href="#"></a></li>
        <li><a href="#" class="active">1</a></li>
        <li><a href="#">2</a></li>
        <li><a href="#">103</a></li>
        <li class="next"><a href="#"></a></li>
      </ul>
    </nav>
  </div>
</template>
