import {mount} from '@vue/test-utils'
import {create<PERSON><PERSON>, setActivePinia} from 'pinia'
import {beforeEach, describe, expect, it, vi} from 'vitest'
import FavoriteAscending from '../FavoriteAscending.vue'
import FavoriteSealed from '../FavoriteSealed.vue'

// Mock the composables
vi.mock('@/composables/useFavoriteItems', () => ({
  default: () => ({
    loading: vi.fn().mockReturnValue(false),
    fetchFavoriteSealed: vi.fn(),
    fetchFavoriteAscending: vi.fn(),
  }),
}))

vi.mock('@/composables/favorite', () => ({
  default: () => ({
    toggleFavorite: vi.fn(),
  }),
}))

vi.mock('@/composables/common', () => ({
  formatDateString: vi.fn().mockReturnValue({
    datePart: '2024/12/24',
    timePart: '23:59',
  }),
}))

// Mock the store
const mockStore = {
  productList: {
    all: [
      {
        exhibition_item_no: 'MOCK001',
        item_no: 'ITEM001',
        auction_classification: 2, // sealed
        free_field: {
          productName: 'Test Product Sealed',
          image_url: '/test-image.jpg',
          condition: '未使用に近い',
          shipping_free: true,
          instant_price: 890000,
          minimum_bid_price: 9000,
        },
        bid_status: {
          current_price: 890000,
          is_top_member: true,
          minimum_bid_exceeded: true,
        },
        attention_info: {
          bid_count: 884,
          favorited_count: 34,
          view_count: 884,
          is_favorited: true,
        },
        start_datetime: new Date().toISOString(),
        end_datetime: new Date(Date.now() + 7200000).toISOString(),
        status: 'active',
        link: '/details/MOCK001',
        currentPrice: '890,000',
      },
      {
        exhibition_item_no: 'MOCK002',
        item_no: 'ITEM002',
        auction_classification: 1, // ascending
        free_field: {
          productName: 'Test Product Ascending',
          image_url: '/test-image2.jpg',
          condition: '中古A',
          shipping_free: false,
          instant_price: 1200000,
          minimum_bid_price: 50000,
        },
        bid_status: {
          current_price: 1200000,
          is_top_member: false,
          minimum_bid_exceeded: true,
        },
        attention_info: {
          bid_count: 156,
          favorited_count: 89,
          view_count: 1205,
          is_favorited: true,
        },
        start_datetime: new Date(Date.now() - 86400000).toISOString(),
        end_datetime: new Date(Date.now() + 3600000).toISOString(),
        status: 'active',
        link: '/details/MOCK002',
        currentPrice: '1,200,000',
      },
    ],
  },
  totalCount: {value: 2},
}

vi.mock('@/composables/useSearchResultState', () => ({
  default: () => mockStore,
}))

describe('Favorite Components', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  describe('FavoriteSealed', () => {
    it('should render sealed auction items only', () => {
      const wrapper = mount(FavoriteSealed)

      // Should show only sealed items (auction_classification === 2)
      const items = wrapper.findAll('.box')
      expect(items).toHaveLength(1)

      // Check if the sealed item is displayed
      expect(wrapper.text()).toContain('Test Product Sealed')
      expect(wrapper.text()).not.toContain('Test Product Ascending')
    })

    it('should display correct item information', () => {
      const wrapper = mount(FavoriteSealed)

      // Check product name
      expect(wrapper.text()).toContain('Test Product Sealed')

      // Check price information
      expect(wrapper.text()).toContain('890,000')
      expect(wrapper.text()).toContain('9,000')

      // Check condition
      expect(wrapper.text()).toContain('未使用に近い')

      // Check shipping free tag
      expect(wrapper.text()).toContain('送料無料')

      // Check status indicators
      expect(wrapper.text()).toContain('あなたがTOP')
      expect(wrapper.text()).toContain('最低落札超え')
    })

    it('should display correct counts', () => {
      const wrapper = mount(FavoriteSealed)

      // Check total count
      expect(wrapper.text()).toContain('2件のオークション')

      // Check individual counts
      expect(wrapper.text()).toContain('884') // view count
      expect(wrapper.text()).toContain('34') // favorite count
      expect(wrapper.text()).toContain('884') // bid count
    })
  })

  describe('FavoriteAscending', () => {
    it('should render ascending auction items only', () => {
      const wrapper = mount(FavoriteAscending)

      // Should show only ascending items (auction_classification === 1)
      const items = wrapper.findAll('.box')
      expect(items).toHaveLength(1)

      // Check if the ascending item is displayed
      expect(wrapper.text()).toContain('Test Product Ascending')
      expect(wrapper.text()).not.toContain('Test Product Sealed')
    })

    it('should display correct item information', () => {
      const wrapper = mount(FavoriteAscending)

      // Check product name
      expect(wrapper.text()).toContain('Test Product Ascending')

      // Check price information
      expect(wrapper.text()).toContain('1,200,000')
      expect(wrapper.text()).toContain('50,000')

      // Check condition
      expect(wrapper.text()).toContain('中古A')

      // Should not show shipping free tag for this item
      expect(wrapper.text()).not.toContain('送料無料')

      // Check status indicators
      expect(wrapper.text()).not.toContain('あなたがTOP') // is_top_member is false
      expect(wrapper.text()).toContain('最低落札超え')
    })

    it('should display correct counts', () => {
      const wrapper = mount(FavoriteAscending)

      // Check total count
      expect(wrapper.text()).toContain('2件のオークション')

      // Check individual counts
      expect(wrapper.text()).toContain('1205') // view count
      expect(wrapper.text()).toContain('89') // favorite count
      expect(wrapper.text()).toContain('156') // bid count
    })
  })
})
