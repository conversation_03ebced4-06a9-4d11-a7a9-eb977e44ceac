<script setup>
  import {computed, defineAsyncComponent} from 'vue'
  import {useSearchResultStore} from '../../stores/search-results'

  const ProductList = defineAsyncComponent(
    () => import('../search-list/ProductList.vue')
  )

  const searchStore = useSearchResultStore()

  const productList = computed(() => searchStore.productList.all)
  const exhibitionList = computed(() => searchStore.productList.exhibitionList)
</script>
<template>
  <section id="list-recommend" class="list-item list-slider">
    <h2>
      <p class="ttl">新着商品</p>
    </h2>
  </section>
</template>
