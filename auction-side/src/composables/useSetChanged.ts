import type {RouteLocationNormalizedLoaded} from 'vue-router'
import {formatDateString} from './common'
import {PATH_NAME} from '../defined/const'
import useSearchResultState from './useSearchResultState'
import type {AttentionInfo, BidStatus, BidHistory} from './useSearchResultState'

/**
 * Composable for handling WebSocket item changes
 * Triggered from websocket when item changed
 * 
 * @returns {Function} Function to handle item changes
 */
export default function useSetChanged() {
  const {productList, productDetails} = useSearchResultState()

  return (
    {
      attention_info,
      bid_status,
      bid_histories,
      exhibition_item_no,
      end_datetime,
    }: {
      attention_info: AttentionInfo
      bid_status: BidStatus
      bid_histories: BidHistory[]
      exhibition_item_no: string
      end_datetime?: string
    },
    route: RouteLocationNormalizedLoaded
  ) => {
    const foundItemIndex = productList.all.findIndex(
      item => String(item.exhibition_item_no) === String(exhibition_item_no)
    )
    if (foundItemIndex !== -1) {
      const updatedItem = {
        ...productList.all[foundItemIndex],
        attention_info,
        bid_status: {
          ...productList.all[foundItemIndex].bid_status,
          ...bid_status,
        },
        currentPrice: bid_status.current_price?.toLocaleString(),
        currentPriceTaxIncluded: Math.round(
          (bid_status.current_price || 0) *
            ((1 + (bid_status.tax_rate || 0)) / 100)
        ).toLocaleString(),
        endDatePart: end_datetime
          ? (formatDateString(end_datetime)?.datePart ?? 'MM/DD')
          : 'MM/DD',
        endTimePart: end_datetime
          ? (formatDateString(end_datetime)?.timePart ?? 'HH:mm')
          : 'HH:mm',
      }

      productList.all.splice(foundItemIndex, 1, updatedItem)
    }
    // console.log('setChanged-->route: ', {route})
    // Handle updates to `productDetails` if standing on the detail page
    if (route.path.includes(PATH_NAME.DETAIL)) {
      if (
        exhibition_item_no &&
        String(productDetails.exhibition_item_no) === String(exhibition_item_no)
      ) {
        productDetails.bid_histories = bid_histories
        productDetails.bid_status = bid_status
        productDetails.currentPrice =
          bid_status.current_price?.toLocaleString() || '0'
        productDetails.currentPriceTaxIncluded = Math.round(
          (bid_status.current_price || 0) *
            (1 + (bid_status.tax_rate || 0) / 100)
        ).toLocaleString()

        productDetails.bid_count = attention_info.bid_count

        if (end_datetime) {
          const extended = formatDateString(end_datetime)
          productDetails.endDatePart = extended.datePart ?? 'MM/DD'
          productDetails.endTimePart = extended.timePart ?? 'HH:mm'
        }
      }
    }
  }
}
