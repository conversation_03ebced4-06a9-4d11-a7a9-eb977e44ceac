import {reactive, ref} from 'vue'

// Type definitions for API responses and data structures
type BidStatus = {
  current_price: number
  bid_price: number
  bid_quantity: number
  tax_rate: number
  pitch_width?: number
  is_top_member?: boolean | null
  minimum_bid_exceeded?: boolean
}

type AttentionInfo = {
  bid_count: number
  favorited_count?: number
  view_count?: number
  is_favorited?: boolean
}

type FreeField = {
  productName: string
  image_url?: string
  start_price?: number
  category?: string
  condition?: string
  shipping_free?: boolean
  instant_price?: number
  minimum_bid_price?: number
}

type BidHistory = {
  bid_price: number
  bid_quantity: number
  bid_datetime: string
  user_id?: string
}

type RawAuctionItem = {
  exhibition_item_no: string
  item_no: string
  category_id: number
  auction_classification?: number
  free_field: FreeField
  bid_status: BidStatus
  attention_info: AttentionInfo
  start_datetime: string
  end_datetime: string
  exhibition_no: string
  bid_histories?: BidHistory[]
  status?: string
}

type FormattedAuctionItem = RawAuctionItem & {
  link: string
  currentPrice: string
  currentPriceTaxIncluded: string
  noOfBids: number
  endDatePart: string
  endTimePart: string
  startDatePart: string
  startTimePart: string
  bidPrice: string
  bidQuantity: string
  bidInputError: {
    bidPrice: string | null
    bidQuantity: string | null
  }
}

type ExhibitionGroup = {
  exhibition_no: string
  exhibition_name: string
  start_datetime: string
  end_datetime: string
}

type ProductList = {
  all: FormattedAuctionItem[]
  exhibitionList: ExhibitionGroup[]
}

type ProductDetails = {
  exhibition_item_no: string
  item_no: string
  category_id: number
  free_field: FreeField
  bid_status: BidStatus
  attention_info: AttentionInfo
  start_datetime: string
  end_datetime: string
  exhibition_no: string
  bid_histories: BidHistory[]
  favorite_count: number
  currentPrice: string
  currentPriceTaxIncluded: string
  bid_count: number
  endDatePart: string
  endTimePart: string
  images: string[]
  freeFields: FreeField
  productName: string
}

// Create a singleton state instance to ensure all composables share the same state
const showCountConstant = 20

const searchKeyTop = ref<string>('')
const searchKeyTopAfter = ref<string>('')
const searchKey = ref<string | null>(null)
const unSoldOut = ref<boolean>(false)
const favorite = ref<boolean | null>(null)
const viewMore = ref<number>(1)
const modelList = reactive<string[]>([])
const categoryList = reactive<string[]>([])
const brandList = reactive<string[]>([])
const showCount = ref<number>(showCountConstant)
const totalCount = ref<number>(0)
const totalHistoryCount = ref<number>(0)
const sorter = ref<string | null>(null)
const startPrice = ref<string | null>(null)
const endPrice = ref<string | null>(null)

const searchCategory = ref<string | null>(null)
const searchBrand = ref<string | null>(null)

const productDetails = reactive<Partial<ProductDetails>>({})
const productDetailsForContact = reactive<Partial<ProductDetails>>({})
const productList = reactive<ProductList>({exhibitionList: [], all: []})

const constants = ref<any[]>([])

const panelView = ref<number>(0)

/**
 * Search result state composable
 * Provides reactive state variables for search results without any methods
 * Uses singleton pattern to ensure all composables share the same state
 *
 * @returns {Object} Object containing all reactive state variables
 */
export default function useSearchResultState() {
  return {
    // Search parameters
    searchKeyTop,
    searchKeyTopAfter,
    searchKey,
    unSoldOut,
    favorite,
    viewMore,
    modelList,
    categoryList,
    brandList,
    showCount,
    totalCount,
    totalHistoryCount,
    sorter,
    startPrice,
    endPrice,
    searchCategory,
    searchBrand,

    // Product data
    productDetails,
    productDetailsForContact,
    productList,

    // Constants and UI state
    constants,
    panelView,

    // Constants
    showCountConstant,
  }
}

// Export types for use in action composables
export type {
  AttentionInfo,
  BidHistory,
  BidStatus,
  ExhibitionGroup,
  FormattedAuctionItem,
  FreeField,
  ProductDetails,
  ProductList,
  RawAuctionItem,
}
