import {formatDateString} from './common'
import {noImg} from '../defined/const'
import useSearchResultState from './useSearchResultState'

/**
 * Composable for setting product details
 * Used when getting data from API [public/refresh-item]
 * 
 * @returns {Function} Function to set product details data
 */
export default function useSetProductDetails() {
  const {productDetails} = useSearchResultState()

  return (searchResult: any) => {
    console.log('searchResult(検索結果): ', searchResult)
    const result = {
      ...searchResult,
      exhibition_item_no: searchResult.exhibition_item_no,
      productName: searchResult?.free_fields?.[0].productName,
      freeFields: searchResult?.free_fields?.[0],
      images: [searchResult?.free_fields?.[0].image_url || noImg],
      startPrice: searchResult?.free_fields?.[0]?.start_price?.toLocaleString(),
      currentPrice: searchResult?.bid_status?.current_price?.toLocaleString(),
      currentPriceTaxIncluded: Math.round(
        searchResult?.bid_status?.current_price *
          (1 + searchResult?.bid_status?.tax_rate / 100)
      )?.toLocaleString(),
      endDatePart:
        formatDateString(searchResult?.bid_status?.end_datetime)?.datePart ??
        'MM/DD',
      endTimePart:
        formatDateString(searchResult?.bid_status?.end_datetime)?.timePart ??
        'HH:mm',
      startDatePart:
        formatDateString(searchResult?.bid_status?.start_datetime)?.datePart ??
        'MM/DD',
      startTimePart:
        formatDateString(searchResult?.bid_status?.start_datetime)?.timePart ??
        'HH:mm',
      preview_end_datetime: searchResult.preview_end_datetime,
      favorite_count: searchResult.attention_info.favorited_count,
      bid_count: searchResult.attention_info.bid_count,
      view_count: searchResult.attention_info.view_count,
      basic_category: searchResult?.free_fields?.[0].basic_category,
      categoryCode: searchResult?.category_code,
      category: searchResult?.category,
      brand: searchResult?.free_fields?.[0].brand,
      itemNo: searchResult?.item_no,
      sold_out: searchResult?.sold_out === 1,
      auction_classification: searchResult?.auction_classification, // オークション方式(1:せり上げ、2:封印入札)
      postage: searchResult.postage
        ? Math.round(
            Number(searchResult?.postage) *
              Number(searchResult?.free_fields?.[0].quantity)
          )?.toLocaleString()
        : null,
    }
    if (
      searchResult?.free_fields?.[0].multi_images &&
      searchResult?.free_fields?.[0].multi_images.length > 0
    ) {
      const multiImages = searchResult?.free_fields?.[0].multi_images
      if (searchResult?.free_fields?.[0].image_url) {
        result.images.push(...multiImages)
      } else {
        // 基本画像がない場合はnoImg上書き
        result.images = multiImages
      }
    }
    Object.assign(productDetails, result)
  }
}
