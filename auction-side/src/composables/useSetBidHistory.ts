import {formatDateString} from './common'
import useSearchResultState from './useSearchResultState'
import type {RawAuctionItem} from './useSearchResultState'

/**
 * Composable for setting bid history
 * Triggered when fetching API [public/get-all-successful-bid-history]
 * 
 * @returns {Function} Function to set bid history data
 */
export default function useSetBidHistory() {
  const {
    productList,
    totalCount,
    searchKeyTop,
    searchKeyTopAfter,
  } = useSearchResultState()

  return (searchResult: any) => {
    const itemList = searchResult?.items ?? []
    totalCount.value = searchResult?.count ?? 0
    searchKeyTopAfter.value = searchKeyTop.value
    const formattedItems = itemList?.map((item: RawAuctionItem) => {
      const {datePart: endDatePart, timePart: endTimePart} = formatDateString(
        item.end_datetime
      )
      const {datePart: startDatePart, timePart: startTimePart} =
        formatDateString(item.start_datetime)
      return {
        ...item,
        link: `/details/${item.exhibition_item_no}`,
        endDatePart,
        endTimePart,
        startDatePart,
        startTimePart,
      }
    })
    // Exhibition list
    productList.exhibitionList = searchResult?.exhibition_group ?? []
    // All items
    productList.all = formattedItems
  }
}
