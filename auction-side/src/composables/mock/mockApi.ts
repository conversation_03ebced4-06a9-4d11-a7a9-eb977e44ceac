// Mock data for development
export const getMockData = (path: string): any => {
  switch (path) {
    case 'public/get-item-search-constants':
      return [
        {key_string: 'PRODUCT_CATEGORY', value1: '1', value2: 'カテゴリ1'},
        {key_string: 'PRODUCT_CATEGORY', value1: '2', value2: 'カテゴリ2'},
        {key_string: 'TAX_RATE', value1: '10', value2: '10%'},
        {key_string: 'PITCH_FOLLOW_BID_PRICE', value1: '100', value2: '100円'},
      ]
    case 'public/search-auction-items':
      return {
        items: [
          {
            exhibition_item_no: 'MOCK001',
            item_no: 'ITEM001',
            category_id: 1,
            auction_classification: 2, // 1: ascending, 2: sealed
            free_field: {
              productName:
                'ルイヴィトン ダミエ アズール ネヴァーフル GM トートバッグ 正規品 アイボリー ホワイト LOUIS VUITTON ショルダー バッグ',
              image_url: '/assets/img/item/top_item01.png',
              condition: '未使用に近い',
              shipping_free: true,
              instant_price: 890000,
              minimum_bid_price: 9000,
            },
            bid_status: {
              current_price: 890000,
              bid_price: 1100,
              bid_quantity: 1,
              tax_rate: 10,
              is_top_member: true,
              minimum_bid_exceeded: true,
            },
            attention_info: {
              bid_count: 884,
              favorited_count: 34,
              view_count: 884,
              is_favorited: true,
            },
            start_datetime: new Date().toISOString(),
            end_datetime: new Date(Date.now() + 7200000).toISOString(), // 2 hours from now
            exhibition_no: 'EXH001',
            status: 'active', // active, soldout, ended
          },
          {
            exhibition_item_no: 'MOCK002',
            item_no: 'ITEM002',
            category_id: 1,
            auction_classification: 1, // 1: ascending, 2: sealed
            free_field: {
              productName:
                'エルメス バーキン30 トゴ ブラック シルバー金具 正規品',
              image_url: '/assets/img/item/top_item02.png',
              condition: '中古A',
              shipping_free: false,
              instant_price: 1200000,
              minimum_bid_price: 50000,
            },
            bid_status: {
              current_price: 1200000,
              bid_price: 1250000,
              bid_quantity: 1,
              tax_rate: 10,
              is_top_member: false,
              minimum_bid_exceeded: true,
            },
            attention_info: {
              bid_count: 156,
              favorited_count: 89,
              view_count: 1205,
              is_favorited: true,
            },
            start_datetime: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
            end_datetime: new Date(Date.now() + 3600000).toISOString(), // 1 hour from now
            exhibition_no: 'EXH002',
            status: 'active',
          },
          {
            exhibition_item_no: 'MOCK003',
            item_no: 'ITEM003',
            category_id: 2,
            auction_classification: 2, // sealed
            free_field: {
              productName:
                'シャネル マトラッセ チェーンショルダーバッグ ラムスキン ブラック',
              image_url: '/assets/img/item/top_item03.png',
              condition: '中古B',
              shipping_free: true,
              instant_price: 450000,
              minimum_bid_price: 30000,
            },
            bid_status: {
              current_price: 450000,
              bid_price: 480000,
              bid_quantity: 1,
              tax_rate: 10,
              is_top_member: true,
              minimum_bid_exceeded: true,
            },
            attention_info: {
              bid_count: 67,
              favorited_count: 23,
              view_count: 445,
              is_favorited: true,
            },
            start_datetime: new Date(Date.now() - *********).toISOString(), // 2 days ago
            end_datetime: new Date(Date.now() + 86400000).toISOString(), // 1 day from now
            exhibition_no: 'EXH003',
            status: 'soldout',
          },
        ],
        total_count: 24,
        current_count: 3,
        status: 'success',
      }
    case 'public/get-new-notices':
      return {
        notices: [
          {
            notice_no: 1,
            title: 'モック通知',
            sub_title: 'テスト用通知',
            body: 'これはモック環境での通知です。',
            create_date: new Date().toISOString(),
            display_code: 1,
            file: [],
            link_url: '',
            title1: 'モック通知',
          },
        ],
        total_count: 1,
        status: 'success',
      }
    case 'private/get-change-info-constants':
      return {
        constants: [
          {key: 'country', value1: 'JP', value2: '日本'},
          {key: 'country', value1: 'US', value2: 'アメリカ'},
        ],
        member: {
          nickname: 'モックユーザー',
          email: '<EMAIL>',
          country: 'JP',
          companyName: 'モック会社',
          tel: '03-1234-5678',
        },
        status: 'success',
      }
    case 'private/change-member-info':
      return {
        status: 'success',
        message: 'Member information updated successfully',
      }
    case 'private/change-member-password':
      return {
        status: 'success',
        message: 'Password changed successfully',
      }
    case 'private/withdraw-member':
      return {
        status: 'success',
        message: 'Member withdrawal completed',
      }
    case '/get-auction-common-constants':
    case 'get-auction-common-constants':
      return [
        {key: 'CURRENCY', value1: 'JPY', value2: '円'},
        {key: 'TIMEZONE', value1: 'Asia/Tokyo', value2: 'JST'},
        {key: 'LANGUAGE', value1: 'ja', value2: '日本語'},
      ]
    case 'get-member-regist-constants':
      return [
        {key_string: 'COUNTRY_CODE', value1: 'JP', value2: '日本'},
        {key_string: 'COUNTRY_CODE', value1: 'US', value2: 'アメリカ'},
        {key_string: 'COUNTRY_CODE', value1: 'CN', value2: '中国'},
      ]
    case 'request-member':
      return {
        status: 'success',
        message: 'Member registration completed successfully',
      }
    case 'private/get-successful-bid-history':
      return {
        items: [
          {
            exhibition_item_no: 'MOCK_BID_001',
            item_no: 'ITEM_BID_001',
            category_id: 1,
            free_field: {
              productName: 'モック落札商品1',
            },
            bid_status: {
              current_price: 5000,
              bid_price: 5500,
              bid_quantity: 1,
              tax_rate: 10,
            },
            attention_info: {
              bid_count: 8,
            },
            start_datetime: new Date(Date.now() - 86400000 * 7).toISOString(),
            end_datetime: new Date(Date.now() - 86400000 * 5).toISOString(),
            exhibition_no: 'EXH_BID_001',
          },
        ],
        count: 1,
        exhibition_group: [],
        isMoreLimit: false,
      }
    default:
      return {
        status: 'success',
        message: 'Mock response for development',
      }
  }
}
