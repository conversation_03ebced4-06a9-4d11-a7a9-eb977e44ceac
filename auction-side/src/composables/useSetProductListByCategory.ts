import {formatDateString} from './common'
import {noImg} from '../defined/const'
import useSearchResultState from './useSearchResultState'
import type {RawAuctionItem} from './useSearchResultState'

/**
 * Composable for setting product list by category
 * Note: This is marked as unused method in the original store
 * 
 * @returns {Function} Function to set product list by category
 */
export default function useSetProductListByCategory() {
  const {
    productList,
    totalCount,
    searchKeyTop,
    searchKeyTopAfter,
  } = useSearchResultState()

  return (categoryId: string, searchResult: any) => {
    const itemList = searchResult?.items ?? []
    totalCount.value = searchResult?.count ?? 0
    searchKeyTopAfter.value = searchKeyTop.value
    const formattedItems = itemList?.map((item: RawAuctionItem) => {
      const {datePart: endDatePart, timePart: endTimePart} = formatDateString(
        item.end_datetime
      )
      const {datePart: startDatePart, timePart: startTimePart} =
        formatDateString(item.start_datetime)
      return {
        ...item,
        category: item.category_id,
        itemNo: item.item_no,
        link: `/details/${item.exhibition_item_no}`,
        productName: item.free_field.productName,
        imgSrc: item.free_field.image_url ?? noImg,
        currentPrice: item.bid_status.current_price?.toLocaleString(),
        currentPriceTaxIncluded: Math.round(
          item.bid_status.current_price +
            item.bid_status.current_price * (item.bid_status.tax_rate / 100)
        )?.toLocaleString(),
        noOfBids: item.attention_info.bid_count,
        endDatePart,
        endTimePart,
        startDatePart,
        startTimePart,
      }
    })
    // Exhibition list
    productList.exhibitionList = searchResult?.exhibition_group ?? []
    // All items
    productList.all = formattedItems
  }
}
