import {ref} from 'vue'
import useApi from './useApi'
import type {ClassificationType} from './useClassificationSwitch'
import useSearchResultState from './useSearchResultState'
import useSetProductList from './useSetProductList'

export default function useFavoriteItems() {
  const loading = ref<boolean>(false)
  const {apiExecute, parseHtmlResponseError} = useApi()
  const state = useSearchResultState()
  const setProductList = useSetProductList()

  const fetchFavorite = async (
    classification: ClassificationType
  ): Promise<void> => {
    loading.value = true

    try {
      const params = {
        favorite: true,
        auction_classification: classification === 'sealed' ? 2 : 1, // sealed auction
        unSoldOut: state.unSoldOut.value,
        initLimit: state.showCount.value * state.viewMore.value,
        languageCode: 'ja',
        limit: state.showCount.value * state.viewMore.value,
      }

      const response = await apiExecute('public/search-auction-items', params)
      setProductList(response)
    } catch (error) {
      parseHtmlResponseError(error)
    } finally {
      loading.value = false
    }
  }

  const fetchAllFavorites = async (): Promise<void> => {
    loading.value = true

    try {
      const params = {
        favorite: true,
        unSoldOut: state.unSoldOut.value,
        initLimit: state.showCount.value * state.viewMore.value,
        languageCode: 'ja',
        limit: state.showCount.value * state.viewMore.value,
      }

      const response = await apiExecute('public/search-auction-items', params)
      setProductList(response)
    } catch (error) {
      parseHtmlResponseError(error)
    } finally {
      loading.value = false
    }
  }

  const refreshFavorites = async (
    classification: ClassificationType
  ): Promise<void> => {
    if (classification === 'ascending') {
      await fetchFavorite('ascending')
    } else if (classification === 'sealed') {
      await fetchFavorite('sealed')
    } else {
      await fetchAllFavorites()
    }
  }

  return {
    loading,
    fetchFavorite,
    fetchAllFavorites,
    refreshFavorites,
  }
}
