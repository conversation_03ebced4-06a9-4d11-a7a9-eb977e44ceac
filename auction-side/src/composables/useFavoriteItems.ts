import {ref} from 'vue'
import useApi from './useApi'
import type {ClassificationType} from './useClassificationSwitch'
import useSearchResultState from './useSearchResultState'
import useSetProductList from './useSetProductList'

/**
 * Composable for managing favorite items
 * Handles API calls to fetch favorite items and integrates with search results store
 *
 * @returns {Object} Object containing loading state and fetch functions
 */
export default function useFavoriteItems() {
  const loading = ref<boolean>(false)
  const {apiExecute, parseHtmlResponseError} = useApi()
  const state = useSearchResultState()
  const setProductList = useSetProductList()

  /**
   * Fetch favorite items for sealed auctions
   * Calls public/search-auction-items API with favorite=true and auction_classification=2
   *
   * @returns {Promise<void>} Promise that resolves when data is fetched and stored
   */
  const fetchFavoriteSealed = async (): Promise<void> => {
    loading.value = true

    try {
      const params = {
        favorite: true,
        auction_classification: 2, // sealed auction
        unSoldOut: state.unSoldOut.value,
        initLimit: state.showCount.value * state.viewMore.value,
        languageCode: 'ja',
        limit: state.showCount.value * state.viewMore.value,
      }

      const response = await apiExecute('public/search-auction-items', params)
      setProductList(response)
    } catch (error) {
      parseHtmlResponseError(error)
    } finally {
      loading.value = false
    }
  }

  /**
   * Fetch favorite items for ascending auctions
   * Calls public/search-auction-items API with favorite=true and auction_classification=1
   *
   * @returns {Promise<void>} Promise that resolves when data is fetched and stored
   */
  const fetchFavoriteAscending = async (): Promise<void> => {
    loading.value = true

    try {
      const params = {
        favorite: true,
        auction_classification: 1, // ascending auction
        unSoldOut: state.unSoldOut.value,
        initLimit: state.showCount.value * state.viewMore.value,
        languageCode: 'ja',
        limit: state.showCount.value * state.viewMore.value,
      }

      const response = await apiExecute('public/search-auction-items', params)
      setProductList(response)
    } catch (error) {
      parseHtmlResponseError(error)
    } finally {
      loading.value = false
    }
  }

  /**
   * Fetch all favorite items (both sealed and ascending)
   * Calls public/search-auction-items API with favorite=true without auction classification filter
   *
   * @returns {Promise<void>} Promise that resolves when data is fetched and stored
   */
  const fetchAllFavorites = async (): Promise<void> => {
    loading.value = true

    try {
      const params = {
        favorite: true,
        unSoldOut: state.unSoldOut.value,
        initLimit: state.showCount.value * state.viewMore.value,
        languageCode: 'ja',
        limit: state.showCount.value * state.viewMore.value,
      }

      const response = await apiExecute('public/search-auction-items', params)
      setProductList(response)
    } catch (error) {
      parseHtmlResponseError(error)
    } finally {
      loading.value = false
    }
  }

  /**
   * Refresh favorite items based on current classification
   * Uses the store's myPageSelectedClassification to determine which type to fetch
   *
   * @returns {Promise<void>} Promise that resolves when data is refreshed
   */
  const refreshFavorites = async (
    classification: ClassificationType
  ): Promise<void> => {
    // const classification = state.myPageSelectedClassification
    // const classification = state.myPageSelectedClassification

    if (classification === 'ascending') {
      await fetchFavoriteAscending()
    } else if (classification === 'sealed') {
      await fetchFavoriteSealed()
    } else {
      await fetchAllFavorites()
    }
  }

  return {
    loading,
    fetchFavoriteSealed,
    fetchFavoriteAscending,
    fetchAllFavorites,
    refreshFavorites,
  }
}
