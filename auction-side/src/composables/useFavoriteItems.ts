import {ref} from 'vue'
import {useSearchResultStore} from '../stores/search-results'
import useApi from './useApi'
import type {ClassificationType} from './useClassificationSwitch'

/**
 * Composable for managing favorite items
 * Handles API calls to fetch favorite items and integrates with search results store
 *
 * @returns {Object} Object containing loading state and fetch functions
 */
export default function useFavoriteItems() {
  const loading = ref<boolean>(false)
  const {apiExecute, parseHtmlResponseError} = useApi()
  const store = useSearchResultStore()

  /**
   * Fetch favorite items for sealed auctions
   * Calls public/search-auction-items API with favorite=true and auction_classification=2
   *
   * @returns {Promise<void>} Promise that resolves when data is fetched and stored
   */
  const fetchFavoriteSealed = async (): Promise<void> => {
    loading.value = true

    try {
      const params = {
        favorite: true,
        auction_classification: 2, // sealed auction
        unSoldOut: store.unSoldOut,
        initLimit: store.showCount * store.viewMore,
        languageCode: 'ja',
        limit: store.showCount * store.viewMore,
      }

      const response = await apiExecute('public/search-auction-items', params)
      store.setProductList(response)
    } catch (error) {
      parseHtmlResponseError(error)
    } finally {
      loading.value = false
    }
  }

  /**
   * Fetch favorite items for ascending auctions
   * Calls public/search-auction-items API with favorite=true and auction_classification=1
   *
   * @returns {Promise<void>} Promise that resolves when data is fetched and stored
   */
  const fetchFavoriteAscending = async (): Promise<void> => {
    loading.value = true

    try {
      const params = {
        favorite: true,
        auction_classification: 1, // ascending auction
        unSoldOut: store.unSoldOut,
        initLimit: store.showCount * store.viewMore,
        languageCode: 'ja',
        limit: store.showCount * store.viewMore,
      }

      const response = await apiExecute('public/search-auction-items', params)
      store.setProductList(response)
    } catch (error) {
      parseHtmlResponseError(error)
    } finally {
      loading.value = false
    }
  }

  /**
   * Fetch all favorite items (both sealed and ascending)
   * Calls public/search-auction-items API with favorite=true without auction classification filter
   *
   * @returns {Promise<void>} Promise that resolves when data is fetched and stored
   */
  const fetchAllFavorites = async (): Promise<void> => {
    loading.value = true

    try {
      const params = {
        favorite: true,
        unSoldOut: store.unSoldOut,
        initLimit: store.showCount * store.viewMore,
        languageCode: 'ja',
        limit: store.showCount * store.viewMore,
      }

      const response = await apiExecute('public/search-auction-items', params)
      store.setProductList(response)
    } catch (error) {
      parseHtmlResponseError(error)
    } finally {
      loading.value = false
    }
  }

  /**
   * Refresh favorite items based on current classification
   * Uses the store's myPageSelectedClassification to determine which type to fetch
   *
   * @returns {Promise<void>} Promise that resolves when data is refreshed
   */
  const refreshFavorites = async (
    classification: ClassificationType
  ): Promise<void> => {
    // const classification = store.myPageSelectedClassification
    // const classification = store.myPageSelectedClassification

    if (classification === 'ascending') {
      await fetchFavoriteAscending()
    } else if (classification === 'sealed') {
      await fetchFavoriteSealed()
    } else {
      await fetchAllFavorites()
    }
  }

  return {
    loading,
    fetchFavoriteSealed,
    fetchFavoriteAscending,
    fetchAllFavorites,
    refreshFavorites,
  }
}
