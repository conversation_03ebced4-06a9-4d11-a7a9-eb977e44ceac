import {formatDateString, priceLocaleString} from './common'
import {noImg, PATH_NAME} from '../defined/const'
import useSearchResultState from './useSearchResultState'
import type {RawAuctionItem} from './useSearchResultState'

/**
 * Composable for setting product list data
 * Used when handling search results from [public/search-auction-items]
 * 
 * @returns {Function} Function to set product list data
 */
export default function useSetProductList() {
  const {
    productList,
    totalCount,
    searchKeyTop,
    searchKeyTopAfter,
  } = useSearchResultState()

  return (searchResult: any) => {
    const itemList = searchResult?.items ?? []
    totalCount.value = searchResult?.count ?? 0
    searchKeyTopAfter.value = searchKeyTop.value
    const formattedItems = itemList?.map((item: RawAuctionItem) => {
      const {datePart: endDatePart, timePart: endTimePart} = formatDateString(
        item.end_datetime
      )
      const {datePart: startDatePart, timePart: startTimePart} =
        formatDateString(item.start_datetime)
      return {
        ...item,
        category: item.category_id,
        itemNo: item.item_no,
        link: `${PATH_NAME.DETAIL}/${item.exhibition_item_no}`,
        productName: item.free_field.productName,
        imgSrc: item.free_field.image_url ?? noImg,
        currentPrice: item.bid_status.current_price?.toLocaleString(),
        currentPriceTaxIncluded: Math.round(
          item.bid_status.current_price +
            item.bid_status.current_price * (item.bid_status.tax_rate / 100)
        )?.toLocaleString(),
        noOfBids: item.attention_info.bid_count,
        endDatePart,
        endTimePart,
        startDatePart,
        startTimePart,
        bidPrice: priceLocaleString(item.bid_status.bid_price, 10), // input bid price value on screen will be saved here
        bidQuantity: priceLocaleString(item.bid_status.bid_quantity, 10), // input bid quantity
        bidInputError: {
          bidPrice: null,
          bidQuantity: null,
        }, // error message for bid price and quantity
      }
    })
    // Exhibition list
    productList.exhibitionList = searchResult?.exhibition_group ?? []
    // All items
    productList.all = formattedItems
  }
}
