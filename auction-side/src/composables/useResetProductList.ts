import useSearchResultState from './useSearchResultState'

/**
 * Composable for resetting product list
 * Resets product list and total count to empty/zero values
 * 
 * @returns {Function} Function to reset product list
 */
export default function useResetProductList() {
  const {productList, totalCount} = useSearchResultState()

  return () => {
    productList.exhibitionList = []
    productList.all = []
    totalCount.value = 0
  }
}
