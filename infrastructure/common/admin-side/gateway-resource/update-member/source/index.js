const Define = require(`${process.env.COMMON_LAYER_PATH}define`);
const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`);
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const pool = new PgPool(process.env.PGHOST);

exports.handle = (e, ctx, cb) => {
  const params = Base.parseRequestBody(e.body);
  const tenantNo = Base.extractTenantId(e);
  console.log(`params = ${JSON.stringify(params)}`);
  console.log('update-member', params.member);
  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        Base.extractTenantId(e)
      )
    )
    .then(() => {
      const sqlParams = [tenantNo, 'member', null];
      return pool
        .rlsQuery(tenantNo, Define.QUERY.GET_FIELD_LIST_FUNCTION, sqlParams)
      .then((result) => {
        console.log('result', result)
        const freeField = Object.assign({}, params.member.freeField);
        const errorList = []
        if (params.validation_mode === true) {
          result.forEach(field => {
            if (field.required_flag && !freeField[field.physical_name]) {
              errorList.push(`${field.logical_name}を入力してください。`)
            }
            if (field.max_length && freeField[field.physical_name] && String(freeField[field.physical_name]).length > field.max_length) {
              errorList.push(`${field.logical_name}は${field.max_length}桁以内で入力してください。`)
            }
            if (field.max_value && freeField[field.physical_name] && Number(freeField[field.physical_name]) > field.max_value) {
              errorList.push(`${field.logical_name}は最大${field.max_value}まで入力してください。`)
            }
            if (field.max_value && freeField[field.physical_name] && Number(freeField[field.physical_name]) > field.max_value) {
              errorList.push(`${field.logical_name}は最大${field.max_value}まで入力してください。`)
            }
            if (field.regular_expressions && freeField[field.physical_name]) {
              console.log(field.regular_expressions, freeField[field.physical_name])
              const regex = new RegExp(String(field.regular_expressions))
              if (field.input_type === 'file') {
                freeField[field.physical_name].forEach(file => {
                  console.log('file', file, regex.test(file))
                  if(!regex.test(file)) {
                    errorList.push(`${field.logical_name}の「${file}」は正規表現と一致しません。`)
                  }
                })
              } else {
                if (!(new RegExp(freeField[field.regular_expressions])).test(field.input_type)) {
                  errorList.push(`${field.logical_name}は正規表現と一致しません。`)
                }
              }
            }
          });
          if (errorList.length > 0) return Promise.reject({
            status  : 400,
            errors : errorList,
          })
          return Promise.reject({
            status  : 200,
            message : '',
          })
        }
        return Promise.resolve()
      })
    })
    .then(() => {
      console.log('GET ORIGINAL MEMBER INFO');
      return pool
        .rlsQuery(Base.extractTenantId(e),Define.QUERY.GET_MEMBER_FULL_FUNCTION, [
          Base.extractTenantId(e),
          params.member.memberRequestNo,
        ])
        .then(members => {
          console.log(`members: ${JSON.stringify(members)}`);
          if (members && members.length > 0) {
            return Promise.resolve();
          }
          return Promise.reject({
            status: 400,
            message: Define.MESSAGE.E000138,
          });
        });
    })
    .then(() => {
      console.log('UPDATE MEMBER');
      const member = params.member;
      if (member.memberNo) {
        // 会員情報更新
        return pool.rlsQuery(Base.extractTenantId(e),Define.QUERY.UPDATE_MEMBER_EDIT_FUNCTION, [
          member.memberNo,
          member.bidAllowFlag,
          member.emailDeliveryFlag,
          member.freeField,
          Base.extractAdminNo(e),
        ]);
      } else {
        // 会員申請情報更新
        return pool.rlsQuery(Base.extractTenantId(e),Define.QUERY.UPDATE_MEMBER_REQUEST_FUNCTION, [
          Base.extractTenantId(e),
          member.memberRequestNo,
          99,
          member.freeField,
          Base.extractAdminNo(e),
        ]);
      }
    })
    .then(result => {
      console.log(`result = ${JSON.stringify(result)}`);
      return Base.createSuccessResponse(cb, result);
    })
    .catch(error => Base.createErrorResponse(cb, error));
};
