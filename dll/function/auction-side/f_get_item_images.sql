CREATE OR R<PERSON>LACE FUNCTION public.f_get_item_images (
    in_exhibition_item_no bigint,
    in_tenant_no bigint,
    in_lang_code character varying
)
RETURNS TABLE(
    file_path character varying
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- 入札会商品画像を一括ダウンロード
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT TIAF.file_path
    FROM t_exhibition_item TEI
    JOIN t_lot TL
      ON TL.lot_no = TEI.lot_no
     AND TL.tenant_no = in_tenant_no
    JOIN t_lot_detail TLD
      ON TLD.lot_no = TEI.lot_no
     AND TLD.tenant_no = in_tenant_no
    JOIN t_item TI
      ON TLD.item_no = TI.item_no
     AND TI.tenant_no = in_tenant_no
    LEFT JOIN t_item_ancillary_file TIAF
      ON TIAF.manage_no = TI.manage_no
     AND TIAF.item_no = TI.item_no
     AND TIAF.tenant_no = in_tenant_no
     AND (TIAF.language_code = in_lang_code OR TIAF.language_code = 'common')
     AND TIAF.delete_flag = 0
     WHERE TEI.exhibition_item_no = in_exhibition_item_no
       AND TIAF.division = 1;
END;

$BODY$;
