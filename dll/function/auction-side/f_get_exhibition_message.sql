CREATE OR REPLACE FUNCTION public.f_get_exhibition_message (
    in_tenant_no bigint,
    in_member_no bigint,
    in_exhibition_item_no bigint
)
RETURNS TABLE(
    exhibition_message_no bigint,
    update_category_id character varying,
    answer_exhibition_message_no bigint,
    message character varying,
    member_no bigint,
    member_nickname text,
    checked_admin_no bigint,
    delete_flag integer,
    no_answer_flag integer,
    message_flag boolean
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- 商品に関するお問い合わせ取得
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT TEM.exhibition_message_no,
         TEM.update_category_id,
         TEM.answer_exhibition_message_no,
         TEM.message,
         TEM.member_no,
         MM.free_field->>'nickname' member_nickname,
         TEM.checked_admin_no,
         TEM.delete_flag,
         TEM.no_answer_flag,
         CASE WHEN (in_member_no IS NULL OR TEM.member_no = in_member_no) THEN TRUE ELSE FALSE END AS message_flag
    FROM t_exhibition_message TEM
    LEFT OUTER JOIN m_member MM
	    ON MM.member_no = TEM.member_no
     AND MM.tenant_no = in_tenant_no
    LEFT OUTER JOIN m_admin MA
	    ON MA.admin_no = TEM.checked_admin_no
     AND MA.tenant_no = in_tenant_no
   WHERE TEM.tenant_no = in_tenant_no
     AND TEM.exhibition_item_no = in_exhibition_item_no
     AND ((TEM.checked_admin_no IS NOT NULL AND TEM.delete_flag = 0 AND TEM.no_answer_flag = 0) OR TEM.create_admin_no IS NOT NULL OR TEM.member_no = in_member_no)
   ORDER BY COALESCE(TEM.answer_exhibition_message_no, TEM.exhibition_message_no), TEM.create_datetime;

END;

$BODY$;
