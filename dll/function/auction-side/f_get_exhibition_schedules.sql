CREATE OR REPLACE FUNCTION public.f_get_exhibition_schedules (
    in_tenant_no bigint,
    in_lang_code character varying
)
RETURNS TABLE(
    exhibition_no bigint,
    tenant_no bigint,
    exhibition_name character varying,
    hold_status integer,
    start_date text,
    end_date text
)

LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- 入札会スケジュール取得
-- Parameters
-- @param in_tenant_no テナント番号
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT TE.exhibition_no,
         TE.tenant_no,
         TEL.exhibition_name,
         CASE WHEN now() < GB.start_datetime THEN 1
              WHEN now() BETWEEN GB.start_datetime AND GB.end_datetime THEN 2
              WHEN now() > GB.end_datetime THEN 3
              ELSE NULL
         END,
         TO_CHAR(GB.start_datetime, 'YYYY/MM/DD'),
         TO_CHAR(GB.end_datetime, 'YYYY/MM/DD')
    FROM t_exhibition TE
    JOIN t_exhibition_localized TEL
      ON TE.exhibition_no = TEL.exhibition_no
    JOIN m_tenant MT
      ON MT.tenant_no = TE.tenant_no
    JOIN (
      SELECT min(TE.exhibition_no) AS exhibition_no,
             min(TE.start_datetime) AS start_datetime,
             max(TE.end_datetime) AS end_datetime
        FROM t_exhibition TE
        JOIN t_exhibition_localized TEL
          ON TE.exhibition_no = TEL.exhibition_no
         AND TEL.language_code = 'ja'
        JOIN m_tenant MT
          ON MT.tenant_no = TE.tenant_no
        WHERE now() BETWEEN COALESCE(TE.preview_start_datetime, TE.start_datetime) AND COALESCE(TE.preview_end_datetime, TE.end_datetime) + interval '3 month'
          AND (TE.delete_flag IS NULL OR TE.delete_flag = 0)
          AND (TEL.delete_flag IS NULL OR TEL.delete_flag = 0)
          AND TE.tenant_no = in_tenant_no
        GROUP BY TEL.exhibition_name
    ) GB
     ON GB.exhibition_no = TE.exhibition_no
   WHERE TEL.language_code = in_lang_code
     ORDER BY TE.end_datetime DESC
   ;
END;

$BODY$;
